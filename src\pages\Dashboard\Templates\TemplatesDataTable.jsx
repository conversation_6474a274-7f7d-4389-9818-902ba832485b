import React, { useEffect } from 'react'
import { useNavigate } from 'react-router-dom';

import { designsTableConfig, defaultTableConfig } from '@constants';
import { useDataTableContext } from '@contexts/DataTableContext';
import { useDeleteTemplate } from '@quires/template';

import { DataTable } from 'primereact/datatable';
import { Tooltip } from 'primereact/tooltip';
import { Column } from 'primereact/column';

import { TfiTrash } from "react-icons/tfi";
import { FiEdit } from 'react-icons/fi';

function TemplatesDataTable() {
    const { totalRecords, lazyParams, setLazyParams, data, dataHandler, loading } = useDataTableContext();
    const navigate = useNavigate();

    const deleteTemplate = useDeleteTemplate()

    useEffect(() => {
        setLazyParams({ ...defaultTableConfig, ...designsTableConfig })
    }, [])

    const deleteAdHandler = async (id) => {
        await deleteTemplate.mutateAsync({ id: id }, {
            onSuccess: () => {
                setLazyParams(prev => ({ ...prev }))
            }
        })
    }

    // Data Table Body Template for actions
    const actionBodyTemplate = (rowData) => {
        return (
            <div className="d-flex justify-center items-center w-full ms-auto">
                {/* Edit */}
                <Tooltip target={`.update-button-${rowData.id}`} showDelay={100} className="fs-8" />
                <button
                    className={`btn btn-sm btn-icon update-button-${rowData.id} me-3`}
                    data-pr-position="bottom"
                    data-pr-tooltip="Edit Template"
                    onClick={() => navigate(`/manager/design-space/${rowData.id}`)}>
                    <FiEdit size={20} />
                </button>

                {/* Delete  */}
                <Tooltip target={`.delete-button-${rowData.id}`} showDelay={100} className="fs-8" />
                <button
                    className={`btn btn-sm btn-icon userType delete-button-${rowData.id}`}
                    data-pr-position="bottom"
                    data-pr-tooltip="Delete"
                    onClick={() => { deleteAdHandler(rowData?.id) }}
                >
                    <TfiTrash size={20} />
                </button>
            </div>
        );
    }

    return (
        <div className="w-full h-full mt-8 flex flex-col">
            <div className='table-responsive text-nowrap flex-1 min-h-0'>
                <DataTable
                    lazy
                    responsiveLayout="stack"
                    breakpoint="960px"
                    dataKey="id"
                    paginator
                    className="table w-full border"
                    value={data}
                    first={lazyParams?.first}
                    rows={lazyParams?.rows}
                    rowsPerPageOptions={[5, 25, 50, 100]}
                    totalRecords={totalRecords}
                    onPage={dataHandler}
                    onSort={dataHandler}
                    sortField={lazyParams?.sortField}
                    sortOrder={lazyParams?.sortOrder}
                    onFilter={dataHandler}
                    filters={lazyParams?.filters}
                    loading={loading}
                    paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
                    bodyClassName="d-flex justify-center"
                    scrollHeight="100%"
                >
                    <Column field="name" header="Name" filter sortable />
                    <Column field="card_type_name" header="Card Type" filter sortable />
                    <Column body={actionBodyTemplate} header="Actions" exportable={false} />
                </DataTable>
            </div>
        </div>
    )
}

export default TemplatesDataTable
